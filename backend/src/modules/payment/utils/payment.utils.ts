import { AddressDto } from 'src/modules/auth/dto/address.dto';
import { CreateCardDto } from 'src/modules/pagarme/dto/create-card.dto';
import { returnOnlyNumbers, splitNumbers } from 'src/utils/pagarme';
import { CreatePaymentDto } from '../dto/create-payment.dto';
import { addDays, currentTimestamp, fromFirebaseTimestamp, toFirebaseTimestamp } from 'src/utils/date.utils';

export function formatDataToSubscription(
  accountId: any,
  customerId: string,
  data: CreatePaymentDto,
  affiliateId?: string,
  parentId?: string,
) {
  return {
    customer_id: customerId,
    plan_id: data.planId,
    payment_method: data.paymentMethod,
    installments: data.installments || '1',
    metadata: {
      accountId,
      implementation: 'false',
      implementation_id: '',
      parcelas: data.installments,
      uid: data.uid,
      planId: data.planId,
      features: data.customFeatures
        .map(({ id, quantity }) => `${id}:${quantity}`)
        .join(','),
      additionals: data.additionals.join(','),
      ...(affiliateId && { affiliateId }),
      ...(parentId && { parentId }),
    },
  };
}

export function convertCreateCardDtoToPagarmeCard(
  data: CreateCardDto,
  customerId: string,
) {
  const billingAddress = prepareBillingAddress(data.billing_address);

  const [cardMonth, cardYear] = splitNumbers(data.cardExpiry!);

  return {
    number: returnOnlyNumbers(data.number),
    holder_name: data.holder_name,
    exp_month: String(cardMonth),
    exp_year: String(cardYear),
    cvv: data.cvv,
    customer_id: customerId,
    billing_address: billingAddress,
  };
}

export function formatDataToCreditCard(
  data: CreatePaymentDto,
  customerId: string,
) {
  const billingAddress = prepareBillingAddress(data);

  const [cardMonth, cardYear] = splitNumbers(data.cardExpiry!);

  return {
    number: returnOnlyNumbers(data.cardNumber!),
    holder_name: data.cardHolderName!,
    exp_month: String(cardMonth),
    exp_year: String(cardYear),
    cvv: data.cardCvc!,
    customer_id: customerId,
    billing_address: billingAddress,
  };
}

export function prepareBillingAddress(data: CreatePaymentDto | AddressDto) {
  if (typeof data === 'object' && 'toBilling' in data && !data.toBilling) {
    return {
      city: data.billingCity!,
      line_2: data.billingComplement!,
      line_1: `${data.billingNumber},${data.billingStreet},${data.billingNeighborhood}`,
      postalCode: data.billingPostalCode!,
      state: data.billingState!,
      country: 'BR',
    };
  }
  return {
    city: data.city,
    line_2: data.complement,
    line_1: `${data.number},${data.street},${data.neighborhood}`,
    postalCode: data.postalCode,
    state: data.state,
    country: 'BR',
  };
}

export function calculateNextBillingDate(billingDay: number, currentDate: number) {

  const baseDate = toFirebaseTimestamp(currentDate || currentTimestamp())!.toDate();

  // Verifica se o billingDay cabe no mês seguinte ou vai para o mês seguinte
  const nextBillingDate = new Date(baseDate.getFullYear(), baseDate.getMonth() + 1, billingDay);

  const diffInDays = Math.ceil((nextBillingDate.getTime() - baseDate.getTime()) / (1000 * 60 * 60 * 24));

  if (diffInDays < 30) {
    // Vai pro mês seguinte
    baseDate.setFullYear(baseDate.getFullYear());
    baseDate.setMonth(baseDate.getMonth() + 1);
    baseDate.setDate(billingDay);
  }

  return baseDate.toISOString();
}

/**
 * Calcula a data de vencimento para uma parcela específica
 * @param installmentNumber Número da parcela (começando em 1)
 * @param billingDay Dia do vencimento
 * @returns Data de vencimento no formato ISO
 */
export function calculateInstallmentDueDate(
  installmentNumber: number,
  billingDay: number,
  currentDate?: number | string,
  isRenewal = false,
): number {
  let dueDate = toFirebaseTimestamp(currentDate || currentTimestamp())!.toDate();

  if (installmentNumber === 1) {
    // Primeira parcela: 3 dias após a data atual
    const dueDays = isRenewal ? 0 : parseInt(process.env.QI_DAY_DUE || '3');
    dueDate = new Date(addDays(dueDate.getTime(), dueDays));
  } else {
    // Da terceira parcela em diante
    const secondInstallmentDate = calculateNextBillingDate(billingDay, dueDate.getTime());
    const baseDate = new Date(secondInstallmentDate);

    const targetMonth = baseDate.getMonth() + (installmentNumber - (isRenewal ? 1 : 2));
    dueDate.setFullYear(baseDate.getFullYear());
    dueDate.setMonth(targetMonth);
    dueDate.setDate(billingDay);

    // Corrige se o dia não existir no mês (tipo 31 em fevereiro)
    if (dueDate.getDate() !== billingDay) {
      dueDate.setDate(0);
    }
  }

  return dueDate.getTime();
}