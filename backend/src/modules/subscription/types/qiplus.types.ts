import { Timestamp } from 'firebase-admin/firestore';
import { Gateway } from 'src/types/gateway.enum';

// Enums permanecem os mesmos
export enum QISubscriptionStatus {
  ACTIVE = 'active',
  FUTURE = 'future',
  CANCELED = 'canceled',
  PENDING = 'pending',
  OVERDUE = 'overdue',
  TRIAL = 'trial',
  EXPIRED = 'expired',
}

export enum QIScheduledAction {
  CANCEL = 'cancel', // Cancela a assinatura
  EXPIRE = 'expire', // Expira a assinatura
  ACTIVATE = 'activate', // Agenda ativação para upgrade/downgrade
}

export enum QIPaymentMethod {
  CREDIT_CARD = 'credit_card',
  BOLETO = 'boleto',
  PIX = 'pix',
}

export enum QIBillingInterval {
  MONTHLY = 'monthly',
  YEARLY = 'yearly',
}

export interface QIAccountConfig {
  data: {
    contacts_max: number;
    yearly_value: number;
    monthly_value: number;
    contacts_min: number;
  };
  modules: {
    shotx: boolean;
  };
  config: any;
}

// Interfaces para Firestore
export interface QISubscriptionItem {
  id: string;
  name: string;
  type: 'module' | 'addon' | 'plan';
  included: number;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
}

export interface QIPayment {
  id: string;
  subscriptionId: string;
  amount: number;
  status: string;
  paymentMethod: QIPaymentMethod;
  paidAt?: Timestamp;
  dueDate: Timestamp;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface QISubscription {
  id: string;
  accountId: string;
  planId: string;
  status: QISubscriptionStatus;
  billingInterval: QIBillingInterval;
  billingDay: number;
  scheduledAction?: QIScheduledAction;
  startDate: Timestamp;
  endDate: Timestamp;
  cycle: number;
  currentPeriodStart: Timestamp;
  currentPeriodEnd: Timestamp;
  canceledAt?: Timestamp;
  trialEnd?: Timestamp;

  // Valores
  currency: string;

  // Método de pagamento
  paymentMethod: QIPaymentMethod;
  installments: number;

  // Relacionamentos (referências do Firestore)
  items: QISubscriptionItem[];

  accountConfig: QIAccountConfig;

  // Metadados
  metadata?: any;

  // Datas de controle
  nextBillingDate: Timestamp;
  lastBillingDate?: Timestamp;
  createdAt: number;
  updatedAt: number;

  isUpgrade?: boolean; // Se a assinatura é um upgrade
  upgradeTo?: string; // ID da assinatura para qual foi feito upgrade
  upgradeCredit?: number; // Valor do desconto de upgrade

  gateway: Gateway;
  cardId?: string;
  customerId?: string;
}

export type QISubscriptionInvoice = {
  id: string;
  chargeId: string;
  transaction_type: string;
  amount: number;
  status: string;
  created_at: Timestamp;
  installment: number;
  upgradeCredit?: number;
};

export type QISubscriptionInvoiceBoleto = QISubscriptionInvoice & {
  url: string;
  barcode: string;
  qr_code: string;
  pdf: string;
  due_at: Timestamp;
  line: string;
};

export type QISubscriptionInvoicePix = QISubscriptionInvoice & {
  qr_code: string;
  url: string;
  due_at: Timestamp;
  identifier: string;
};

export type QISubscriptionInvoiceCreditCard = QISubscriptionInvoice & {
  identifier: string;
};

export type QISubscriptionToProcess = QISubscription & {
  action: QIScheduledAction | 'billing';
};

export class QISubscriptionToProcessDto {
  id: string;
  cycle: number;
  accountId: string;
  paymentMethod: QIPaymentMethod;
  installments: number;
  cardId: string;
  customerId: string;
  items: QISubscriptionItem[];
  billingInterval: QIBillingInterval;
  action: QIScheduledAction | 'billing';
}

export class QISubscriptionToProcessDtoList {
  subscriptions: QISubscriptionToProcessDto[];
}
