import { CheckoutFormData, PaymentMethod } from "@/components/checkout/types";
import { Button } from "@/components/ui/button";
import { countryCodesData } from "@/data/countries";
import { initialFormValues } from "@/data/initialFormValues";
import { useToast } from "@/hooks/use-toast";
import { processSubscriptionResponse } from "@/lib/payment.utils";
import { parsePhone } from "@/lib/phone.utils";
import { PlanModel } from "@/models/Plan";
import { accountsRepository } from "@/repositories/accountsRepository";
import { additionalsRepository } from "@/repositories/additionals";
import { subscriptionService } from "@/services/subscription";
import { Additional } from "@/types/additional-types";
import { PaymentProcessStatus, PaymentStatus } from "@/types/payment.type";
import { Plan } from "@/types/plan-types";
import { ViaCEPResponse } from "@/types/viacep-types";
import axios from "axios";
import { useCallback, useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { CheckoutProviderProps, CountryCode } from "./checkout-context-types";
import { useAuth } from "../auth/useAuth";
import { CheckoutContext } from "./useCheckout";
import { useNavigate } from "react-router-dom";
import { plansService } from "@/services/plan";
import { getCurrentPlanOnMonthly, getCurrentPlanOnYearly, getTotalPrice, isPartialUpgrading } from "@/lib/plan.utils";
import { nowDate, toLocalISOString } from "@/lib/date.utils";

export function CheckoutProvider({ children }: CheckoutProviderProps) {
  const navigate = useNavigate();
  const { user, account, subscription, register, loginWithToken, userData, loading, setSubscription, refreshSubscription } = useAuth();

  const [plans, setPlans] = useState<Plan[]>([]);
  const [currentPlan, setCurrentPlan] = useState<Plan | null>(null);
  const [selectedPlan, setSelectedPlan] = useState<Plan | null>(null);
  const [selectedPlanInMonthly, setSelectedPlanInMonthly] = useState<Plan | null>(null);
  const [selectedPlanInYearly, setSelectedPlanInYearly] = useState<Plan | null>(null);
  const isYearly = selectedPlan ? selectedPlan?.isYearly : true;

  const [currentStep, changeStep] = useState(0);
  const [additionals, setAdditionals] = useState<Additional[]>([]);
  const [countryCodes, _] = useState<CountryCode[]>(countryCodesData);
  const [selectedCountryCode, setSelectedCountryCode] = useState("+55");
  const [toBilling, setToBilling] = useState(true);
  const [autoCompletedAddress, setAutoCompletedAddress] = useState<Record<
    string,
    ViaCEPResponse
  > | null>(null);
  const [lastStepForm, setLastStepForm] = useState(2);
  const [creatingUser, setCreatingUser] = useState(false);
  const selectedCountry =
    countryCodes.find((country) => country.code === selectedCountryCode) || countryCodes[0];

  const [payment, setPayment] = useState<PaymentProcessStatus>({
    status: PaymentStatus.STOPPED,
    error: null,
    subscription: null,
    processing: false,
    pix: null,
    skipPayment: false,
  });

  const { toast } = useToast();

  useEffect(() => {
    if (payment.status !== PaymentStatus.STOPPED) return;
    setPayment((prev) => ({
      ...prev,
      status: PaymentStatus.STOPPED,
    }));
  }, [payment.status]);

  useEffect(() => {
    if (loading) return;
    const billingDay = form.watch('billingDay')
    plansService
      .getPlans({
        leadsCount: selectedPlan?.leadsCount || 0,
        additionals: additionals.map((item) => item.id) || [],
        isYearly: isYearly,
        customFeatures: selectedPlan?.customFeatures || [],
        billingDay: billingDay,
      })
      .then((plans) => {
        setPlans(plans.map((plan) => PlanModel.parse(plan)));
      });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [loading]); // Se colocar o selectedPlan, vai dar erro no valor dos personalizados

  useEffect(() => {
    if (selectedPlan) return
    if (subscription?.planId) {
      const isYearly = subscription.billingInterval === "yearly";
      const plan = plans.find((plan) => plan.id === subscription.planId && plan.isYearly === isYearly);
      if (plan) {
        setSelectedPlan(plan);
      }
    } else if (account?.planId) {
      const plan = plans.find((plan) => plan.id === account?.planId);
      if (plan) {
        setSelectedPlan(plan);
      }
    } else if (!selectedPlan) {
      setSelectedPlan(plans[0]);
    }
  }, [subscription?.planId, plans]);

  useEffect(() => {
    if (selectedPlan) {
      setSelectedPlanInMonthly(getCurrentPlanOnYearly(plans, selectedPlan));
      setSelectedPlanInYearly(getCurrentPlanOnMonthly(plans, selectedPlan));
    }
  }, [selectedPlan, additionals]);

  useEffect(() => {
    additionalsRepository.getAdditionals().then((additionals) => {
      setAdditionals(additionals);
    });
  }, []);

  useEffect(() => {
    if (subscription?.planId && plans.length > 0) {
      const isYearly = subscription.billingInterval === "yearly";
      setCurrentPlan(plans.find((plan) => plan.id === subscription.planId && plan.isYearly === isYearly) || null);
    }
  }, [subscription?.planId, plans]);

  const paySubscription = async () => {
    const now = nowDate()
    const valid = await form.trigger();
    if (!valid) {
      return;
    }

    const data = form.getValues();

    setPayment({
      status: PaymentStatus.PROCESSING,
      error: null,
      subscription: null,
      processing: true,
    });

    const selectedBillingCountry =
      countryCodes.find((country) => country.code === data.billingCountry) || countryCodes[0];

    const paymentData = {
      country: selectedCountry.value,
      phoneCountryCode: "+55",
      billingCountry: selectedBillingCountry.value,
      paymentMethod: data.paymentMethod,
      isYearly,
      isCompany: data.isCompany,
      discount: data.discount,
      companyPhoneCountryCode: data.companyPhoneCountryCode,
      installments: Number(data.installments),
      name: data.name,
      email: data.email,
      cpf: data.cpf,
      birthdate: data.birthdate || userData?.birthdate,
      phone: data.phone,
      postalCode: data.postalCode,
      street: data.street,
      number: data.number,
      complement: data.complement,
      neighborhood: data.neighborhood,
      city: data.city,
      state: data.state,
      cardHolderName: data.cardHolderName,
      cardId: data.cardId,
      cardNumber: data.cardNumber?.replace(/\D/g, ""),
      cardExpiry: data.cardExpiry,
      cardCvc: data.cardCvc,
      userId: user?.uid,
      accountId: account?.id,
      uid: user?.uid,
      planId: selectedPlan.id,
      toBilling: toBilling,
      additionals: additionals.filter((item) => item.selected).map((item) => item.id),
      customFeatures: selectedPlan.customFeatures.map((item) => ({
        id: item.id,
        quantity: item.quantity,
      })),
      leadsCount: selectedPlan.leadsCount,
      companyCnpj: data.companyCnpj?.replace(/\D/g, ""),
      billingPostalCode: data.billingPostalCode,
      billingStreet: data.billingStreet,
      billingCity: data.billingCity,
      billingComplement: data.billingComplement,
      billingNeighborhood: data.billingNeighborhood,
      billingNumber: data.billingNumber,
      billingState: data.billingState,
      billingDay: data.billingDay,
      clientTimestamp: toLocalISOString(now),
    };

    if (data.paymentMethod === PaymentMethod.CREDIT_CARD) {
      paymentData.billingDay = now.getDate();
    }

    try {
      const response = await subscriptionService.processSubscription(paymentData);

      if (response.error) {
        setPayment({
          status: PaymentStatus.ERROR,
          processing: payment.processing,
        });
        return;
      }

      if (response.skipPayment) {
        refreshSubscription()
        setPayment({
          status: PaymentStatus.SUCCESS,
          processing: payment.processing,
          skipPayment: true,
        });
        return;
      }

      const processedSubscription = processSubscriptionResponse(response.data);
      const subscriptionListener = accountsRepository.listenAccount(account?.id, (account) => {
        if (!account) return;

        const paymentStatus = processedSubscription.isUpgrade
          ? account.payment_upgrade_status
          : account.payment_status;
        if (!paymentStatus) return;
        if (paymentStatus.subscription_id !== processedSubscription.id) return;

        if (
          paymentData.paymentMethod === PaymentMethod.PIX &&
          paymentStatus.status === "pending" &&
          paymentStatus.pix
        ) {
          setPayment((prev) => ({
            ...prev,
            status: PaymentStatus.WAITING,
            subscription: processedSubscription,
            pix: paymentStatus.pix,
          }));
        }

        if (paymentData.paymentMethod === PaymentMethod.BOLETO) {
          subscriptionListener();
          setPayment((prev) => ({
            ...prev,
            status: PaymentStatus.STOPPED,
            subscription: processedSubscription,
          }));
          navigate("/subscription", { replace: true });
          return;
        }

        if (["pending", "pending_payment"].includes(paymentStatus.status)) return;

        // if (paymentStatus.status === "pending") return;
        processedSubscription.updatePaymentStatus(paymentStatus.status as "paid" | "failed");
        if (paymentStatus.status === "paid") {
          refreshSubscription()
          setPayment((prev) => ({
            ...prev,
            status: PaymentStatus.SUCCESS,
            subscription: processedSubscription,
          }));
        }

        if (paymentStatus.status === "failed") {
          setPayment((prev) => ({
            ...prev,
            status: PaymentStatus.ERROR,
            error: paymentStatus.error_message,
          }));
        }
        subscriptionListener();
      });
    } catch (error) {
      console.error("Payment error:", error);
      setPayment({
        status: PaymentStatus.ERROR,
        processing: payment.processing,
      });
    }
  };

  const handleBack = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const onLeadsChange = (uniquePlanId: string, quantity: number) => {
    const index = plans.findIndex(({ uniqueId }) => uniqueId === uniquePlanId)
    const plan = plans[index]
    const updatedPlan = PlanModel.recriate({ ...plan, leadsCount: quantity })
    const updatedPlans = plans
    updatedPlans[index] = updatedPlan
    setPlans(updatedPlans)
    setSelectedPlan(updatedPlan);
  };

  const moreSavingYearly = plans.reduce((acc, plan) => {
    return plan.discount > acc.discount ? plan : acc;
  }, plans[0]);

  const form = useForm<CheckoutFormData>({
    defaultValues: initialFormValues,
    mode: "onChange",
  });

  useEffect(() => {
    if (userData) {
      form.reset({
        ...initialFormValues,
        ...userData,
        billingDay: subscription?.billingDay || initialFormValues.billingDay,
        acceptTerms: true,
      });
    }
  }, [userData, form]);

  useEffect(() => {
    if (account) {
      setPlans(
        plans.map((plan) => {
          return {
            ...plan,
            customFeatures: plan.customFeatures.map((feature) => ({
              ...feature,
              quantity: account.config[feature.id] || feature.quantity,
            })),
          };
        }),
      );
      setAdditionals(
        additionals.map((item) => ({
          ...item,
          selected: Boolean(account.modules[item.id.replace("-module", "")]),
        })),
      );
    }
  }, [account]);

  const handleCEPChange = useCallback(
    async (event: React.ChangeEvent<HTMLInputElement>) => {
      const cep = event.target.value.replace(/\D/g, "");

      if (cep.length === 8) {
        try {
          const response = await axios.get<ViaCEPResponse>(`https://viacep.com.br/ws/${cep}/json/`);
          const data = response.data;

          if (data.erro) {
            toast({
              title: "CEP não encontrado",
              description: "Por favor, verifique o CEP informado",
              variant: "destructive",
            });
            return;
          }

          setAutoCompletedAddress((prev) => ({ ...prev, [cep]: data }));
          if (!toBilling) {
            form.setValue("billingStreet", data.logradouro);
            form.setValue("billingNeighborhood", data.bairro);
            form.setValue("billingCity", data.localidade);
            form.setValue("billingState", data.uf);
          } else {
            form.setValue("street", data.logradouro);
            form.setValue("neighborhood", data.bairro);
            form.setValue("city", data.localidade);
            form.setValue("state", data.uf);
          }

          toast({
            title: "Endereço encontrado",
            description: "Os campos foram preenchidos automaticamente",
          });
        } catch (error) {
          toast({
            title: "Erro ao buscar CEP",
            description: "Ocorreu um erro ao buscar o endereço. Tente novamente.",
            variant: "destructive",
          });
        }
      }
    },
    [toBilling, toast, form],
  );

  useEffect(() => {
    form.register("postalCode", {
      onChange: handleCEPChange,
    });
    form.register("billingPostalCode", {
      onChange: handleCEPChange,
    });
  }, [form, handleCEPChange]);

  const createUser = async () => {
    const companyPhone = parsePhone(
      `${form.getValues("companyPhoneCountryCode")} ${form.getValues("companyPhone")}`,
    );
    const mobile = parsePhone(`${form.getValues("phoneCountryCode")} ${form.getValues("phone")}`);

    const shotxModule = additionals.find((item) => item.id === "shotx-module")?.selected;

    const affiliateId = localStorage.getItem("affiliateId") || "";
    const parentId = localStorage.getItem("parentId") || "";
    const data = {
      email: form.getValues("email"),
      password: form.getValues("password"),
      name: form.getValues("name"),
      isCompany: form.getValues("isCompany"),
      birthdate: form.getValues("birthdate"),
      cpf: form.getValues("cpf")?.replace(/\D/g, ""),
      isYearly: isYearly,
      discount: form.getValues("discount"),
      shotXModule: shotxModule ? 1 : 0,
      additionals: additionals.filter((item) => item.selected).map((item) => item.id),
      customs: selectedPlan.customFeatures.map((item) => ({
        id: item.id,
        quantity: item.quantity,
      })),
      leadsCount: selectedPlan.leadsCount,
      address: {
        city: form.getValues("city"),
        complement: form.getValues("complement"),
        country: form.getValues("country"),
        neighborhood: form.getValues("neighborhood"),
        number: form.getValues("number"),
        postalCode: form.getValues("postalCode"),
        state: form.getValues("state"),
        street: form.getValues("street"),
      },
      company: {
        name: form.getValues("companyName") || "",
        cnpj: form.getValues("companyCnpj")?.replace(/\D/g, "") || "",
        phone: companyPhone?.national || "",
        phoneCountryCode: companyPhone?.countryCode || "",
        email: form.getValues("companyEmail") || "",
      },
      planId: selectedPlan.id,
      mobileCC: mobile?.countryCode,
      mobile: mobile?.national,
      affiliateId,
      parentId,
    };

    register(data)
      .then((response) => {
        if (response.error) {
          if (response.statusCode === 409) {
            form.setError("email", {
              message: "O email informado já está em uso",
            });
            const emailStep = form.getValues("isCompany") ? 3 : 2;
            setCurrentStep(emailStep);
            toast({
              title: "Email já existe",
              description:
                "O email informado já está em uso. Por favor, use outro email ou faça login",
              variant: "destructive",
            });
          } else {
            toast({
              title: "Erro ao criar usuário",
              description:
                "Ocorreu um erro ao criar o usuário. Por favor, tente novamente ou entre em contato com o suporte",
              variant: "destructive",
            });
          }
          return;
        }

        if (response.token) {
          loginWithToken(response.token);
        }
      })
      .catch((error) => {
        console.log("error", error);
      })
      .finally(() => {
        setCreatingUser(false);
      });
  };

  const setCurrentStep = async (step: number) => {
    // Caso o usuário seja empresa e volte para o passo 1, desregistra o birthdate
    const hasBirthDate = !!form.getValues("birthdate");
    if (!hasBirthDate && currentStep === 2 && step === 1) {
      form.unregister("birthdate");
    }

    const from = form.getValues("isCompany") ? 5 : 4;
    const create = currentStep === from && step === from + 1;
    if (!account && create && !creatingUser) {
      setCreatingUser(true);
      await createUser()
    }
    changeStep(step); // Caso precise esperar a criação da conta, mudar essa linha para o final da criação
  };

  const discount = form.watch("discount") || 0;

  const additionalsCalculated = additionals.map((item) => {
    return {
      ...item,
      price: (item.price - item.discountValue) * (isYearly ? 12 : 1),
    };
  });

  const planPrice = getTotalPrice(selectedPlan);

  const subtotal =
    planPrice +
    additionalsCalculated.filter((i) => i.selected).reduce((acc, item) => acc + item.price, 0);

  const isUpgrade = currentPlan && currentPlan?.config?.order < selectedPlan?.config?.order;
  const isDowngrade = currentPlan && currentPlan?.config?.order > selectedPlan?.config?.order;
  const isPartialUpgrade = isPartialUpgrading(currentPlan, selectedPlan)
  const changingBillingCycle = !!currentPlan && currentPlan?.isYearly !== isYearly;
  const upgradeCredit = (isUpgrade || isPartialUpgrade) && !changingBillingCycle && currentPlan?.credit;
  const total = subtotal - discount - (upgradeCredit || 0);

  const remainingInstallments = ((subscription?.installments || 0 - subscription?.cycle || 0) || 12) - 1;

  const changingBillingDay = Number(form.getValues('installments')) > 1 && Number(form.getValues('billingDay')) !== new Date().getDate();

  const resetCheckout = () => {
    window.location.reload();
  };

  const STEPS_INDIVIDUAL = [
    "Tipo de Cadastro",
    "Informações Pessoais",
    "Endereço",
    "Serviços Adicionais",
    "Dados de Pagamento",
  ];

  const STEPS_COMPANY = [
    "Tipo de Cadastro",
    "Informações da Empresa",
    "Informações do Responsável",
    "Endereço do Responsável",
    "Serviços Adicionais",
    "Dados de Pagamento",
  ];

  const steps = form.watch("isCompany") ? STEPS_COMPANY : STEPS_INDIVIDUAL;


  const switchToYearlyPlan = (toYearly: boolean) => {
    const plan = plans.find(({ id, isYearly }) => id === selectedPlan?.id && isYearly === toYearly);
    if (plan) {
      setSelectedPlan(plan);
    }
  };

  /**
   * Reactivate subscription
   */
  const reactivateSubscription = async () => {
    if (!subscription) return;

    try {
      const data = await subscriptionService.activeSubscription(subscription?.id);
      setSubscription(data);

      toast({
        title: "🎉 Assinatura Ativada com Sucesso!",
        description: (
          <div>
            <p>Obrigado por ativar sua assinatura! 💫</p>
            <p>
              É uma alegria ter você com a gente. A partir de agora, você tem
              acesso completo a todos os benefícios e conteúdos exclusivos que
              preparamos com muito carinho.
            </p>
            <p>
              Se precisar de qualquer coisa, nossa equipe está aqui para te
              ajudar. Aproveite ao máximo — essa jornada está só começando!
            </p>
            <p>✨ Bem-vindo(a) à experiência premium! 💫</p>
          </div>
        ),
        duration: 3000,
      });
      return data;
    } catch (error) {
      toast({
        title: "Ativação negada!",
        description:
          "Algo aconteceu ao tentar realizar sua ativação, por favor tente mais tarde. Se persistir, entre em contato com o suporte.",
        variant: "destructive",
        duration: 3000,
      });
    }
  };

  const isSamePlan = currentPlan?.uniqueId === selectedPlan?.uniqueId && !isPartialUpgrade;

  return (
    <CheckoutContext.Provider
      value={{
        plans,
        setPlans,
        selectedPlan,
        setSelectedPlan,
        selectedPlanInMonthly,
        selectedPlanInYearly,
        isYearly,
        switchToYearlyPlan,
        currentStep,
        setCurrentStep,
        additionals,
        setAdditionals,
        handleBack,
        onLeadsChange,
        moreSavingYearly,
        countryCodes,
        selectedCountry,
        setSelectedCountryCode,
        form,
        autoCompletedAddress,
        toBilling,
        setToBilling,
        lastStepForm,
        setLastStepForm,
        payment,
        setPayment,
        paySubscription,
        resetCheckout,
        additionalsCalculated,
        subtotal,
        total,
        discount,
        isCompany: form.watch("isCompany"),
        steps,
        currentPlan,
        isSamePlan,
        isCurrentYearly: subscription?.billingInterval === "yearly",
        isUpgrade,
        isPartialUpgrade,
        isDowngrade,
        upgradeCredit,
        remainingInstallments,
        reactivateSubscription,
        changingBillingDay,
      }}
    >
      {children}
    </CheckoutContext.Provider>
  );
}
