import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { cn, formatCurrency, formatCurrencyToShow } from '@/lib/utils';
import { Plan } from '@/types/plan-types';
import { motion } from 'framer-motion';
import { Check } from 'lucide-react';
import { LeadsCapacityControl } from './leads/LeadsCapacityControl';
import { PlanBadge } from './plan/PlanBadge';
import { SecurityBadges } from './security/SecurityBadges';
import { useAuth } from '@/contexts/auth/useAuth';
import { getTotalPrice, isPartialUpgrading, isSamePlan } from '@/lib/plan.utils';
import { t } from '@/lib/translations.helper';
import { useState } from 'react';
import { currentTimestamp, formatDate } from '@/lib/date.utils';

interface PlanCardProps {
  plan: Plan;
  isYearly?: boolean;
  currentPlan?: Plan;
  isCurrentYearly?: boolean;
  selectedPlan?: Plan;
  onSelect: () => void;
  onLeadsChange?: (planId: string, quantity: number) => void;
  onCustomize?: () => void;
  remainingInstallments?: number;
}

export function PlanCard({
  plan,
  currentPlan,
  isCurrentYearly,
  selectedPlan,
  onSelect,
  onLeadsChange,
  onCustomize,
  remainingInstallments,
}: PlanCardProps) {
  const { uniqueId, name, tag, img, description, features, credit } = plan;

  const [leadsChanged, setLeadsChanged] = useState(false);

  const handleLeadsChange = (planId: string, quantity: number) => {
    const initialLeads = plan.options[0].contacts_max;
    setLeadsChanged(quantity !== initialLeads);
    onLeadsChange?.(planId, quantity);
  };

  const planPrice = getTotalPrice(plan); // Now in cents

  // For yearly plans, divide by 12 to get monthly price in cents
  const firstAmountDiff = (planPrice / (plan?.isYearly ? 12 : 1)) - (credit || 0); // Valor a ser pago em centavos
  const hasDiff = firstAmountDiff > 0;

  const { subscription } = useAuth();
  const nextBillingDate = subscription?.nextBillingDate || currentTimestamp();

  const isSelected = selectedPlan?.uniqueId === plan.uniqueId;
  const isCurrentPlan = currentPlan?.uniqueId === plan.uniqueId;

  const disabled = false;

  // Função para determinar o nível real do plano considerando se é mensal ou anual
  const getPlanLevel = (planToCheck: Plan | undefined) => {
    if (!planToCheck) return 0;
    // Multiplicamos por 100 para criar uma separação clara entre planos mensais e anuais
    // Planos anuais terão nível 100+ maior que seus equivalentes mensais
    return planToCheck.config?.order + (planToCheck.isYearly ? 100 : 0);
  };

  // Obtém o nível do plano atual e do plano sendo visualizado
  const currentPlanLevel = getPlanLevel(currentPlan);
  const viewedPlanLevel = getPlanLevel(plan);

  // Determina se é upgrade ou downgrade baseado nos níveis reais
  const isUpgrade = currentPlan && (currentPlanLevel < viewedPlanLevel);
  const isDowngrade = currentPlan && (currentPlanLevel > viewedPlanLevel);

  // // Verifica se o plano selecionado tem mais recursos que o plano atual
  // Determina se é um upgrade do mesmo plano (apenas adicionando mais recursos)
  const isPartialUpgrade = isPartialUpgrading(currentPlan, plan) || leadsChanged;

  // Determine if the upgrade/downgrade will be immediate or scheduled
  const isMonthlyToMonthly = !isCurrentYearly && !plan.isYearly;
  const isYearlyToYearly = isCurrentYearly && plan.isYearly;

  // Immediate upgrade cases: same billing cycle (monthly->monthly or yearly->yearly) with higher plan
  // Também verificamos se os planos são do mesmo tipo (mensal ou anual) usando a diferença de nível
  const isSamePlanType = Math.abs(viewedPlanLevel - currentPlanLevel) < 100;
  const isImmediateUpgrade = hasDiff && (isUpgrade && isSamePlanType && (isMonthlyToMonthly || isYearlyToYearly)) || isPartialUpgrade;

  // Scheduled upgrade cases: different billing cycle (monthly->yearly) or downgrade
  const isScheduledUpgrade = isUpgrade && !isImmediateUpgrade;
  const isScheduledDowngrade = isDowngrade;

  // Format the next billing date for display
  const formattedNextBillingDate = nextBillingDate ?
    formatDate(nextBillingDate) : '';

  const isUpgrading = isUpgrade || isPartialUpgrade
  const changingBillingCycle = !!currentPlan && currentPlan?.isYearly !== plan.isYearly;

  const hasAdditionals = plan.customFeatures.some((f) => f.quantity > f.included);
  const additionalsPrice = plan.customFeatures
    .filter((f) => f.quantity > f.included)
    .reduce((acc, feature) => acc + (plan.isYearly ? feature.yearlyPrice * 12 : feature.monthlyPrice) * (feature.quantity - feature.included), 0);

  return (
    <Card
      className={cn(
        'relative flex flex-col rounded-lg shadow-lg hover:scale-[1.02] cursor-pointer transition-transform duration-300 h-full',
        isSelected ? 'shadow-[#0071e2] scale-[1.01]' : '',
      )}
      onClick={disabled || isSamePlan(currentPlan, plan) ? undefined : onSelect}
    >
      {plan.discount > 0 && (
        <div className="absolute -top-3 right-4 flex items-center gap-1 bg-orange-500 dark:bg-gradient-to-r dark:from-orange-900 dark:to-orange-600  px-3 py-1 rounded-full text-white text-xs font-medium shadow-md">
          🔥&nbsp;Economize {plan.discountPercentage.toFixed(0)}% no plano anual
        </div>
      )}
      <CardHeader>
        {img && <div className="mb-4 -mt-2">
          <img src={img} alt={name} className="w-full h-auto rounded-lg shadow-lg" />
        </div>}
        <div className="space-y-2 flex flex-col items-center">
          <CardTitle className="flex items-center justify-center gap-2">
            <span className="whitespace-nowrap">{name}</span>
          </CardTitle>
          <div className="mt-1">
            <PlanBadge tagIcon={''} tag={tag} />
          </div>
        </div>
        <CardDescription className="text-center">{description}</CardDescription>
      </CardHeader>

      <CardContent className="flex-grow">

        <div className="mb-4">
          <LeadsCapacityControl
            planId={uniqueId}
            initialValue={plan.leadsCount}
            options={plan.options}
            onChange={handleLeadsChange}
          />
        </div>

        <ul className="space-y-2">
          {features
            .filter((feature) => feature.included && feature.showOnCard)
            .slice(0, 5)
            .map((feature, index) => (
              <li key={index} className="flex items-center gap-2">
                <Check className={'h-4 w-4 text-[#0071e2]'} />
                <span className={'text-foreground'}>{feature.name}</span>
              </li>
            ))}
        </ul>
        <div className="flex flex-col gap-2 mt-4">
          {/* {!isUpgrading && !changingBillingCycle ? (
            <>
              <span className="text-md text-green-600 font-medium">
                Oferta especial
              </span>
              <div className="text-sm text-muted-foreground">
                <span className="text-sm text-muted-foreground">
                  de <span className='line-through'>
                    {formatCurrencyToShow(planPrice, plan?.isYearly)}
                  </span>
                  por
                </span>
              </div>

              {credit > 0 && <div className="flex items-baseline gap-1">
                <span className="text-4xl font-bold text-green-600">
                  {formatCurrencyToShow(planPrice / (plan?.isYearly ? 12 : 1) - credit, plan?.isYearly)}
                </span>
                {plan?.isYearly && remainingInstallments > 0 && <div className="flex items-center gap-1">
                  <span className="text-xs text-muted-foreground font-medium">
                    + {remainingInstallments}x {formatCurrencyToShow(planPrice, plan?.isYearly)}
                  </span>
                </div>}
              </div>}
              {plan?.isYearly && (
                <span className="text-sm text-muted-foreground">
                  ou {formatCurrency(planPrice)} a vista
                </span>
              )}
              {credit > 0 && <div className="flex items-center gap-1">
                <span className="text-xs text-green-600 font-medium">
                  com {formatCurrency(credit)} de desconto do seu plano atual
                </span>
              </div>}
              <p className="text-xs text-amber-600 font-medium mt-1">
                * e a partir de {formatDate(nextBillingDate)}
                {' - '}
                {formatCurrencyToShow(planPrice, plan?.isYearly)}
                /mês
              </p>
            </>
          ) : ( */}
          <>
            <span className="text-sm text-muted-foreground">A partir de</span>
            <div className="flex items-baseline gap-1">
              <span className="text-4xl font-bold">
                {formatCurrencyToShow(planPrice, plan?.isYearly)}
              </span>
              <span className="text-muted-foreground">/mês</span>
            </div>
            {plan?.isYearly && (
              <span className="text-sm text-muted-foreground">
                ou {formatCurrency(planPrice)} a vista
              </span>
            )}
          </>
          {/* )} */}
          {plan?.isYearly && plan.discount > 0 && !plan.credit && (
            <p className="text-sm text-green-600 font-medium mt-1">
              🎉 Economia de {formatCurrency(plan.discount)}
              <span className="text-xs">/ano</span>
            </p>
          )}
        </div>
      </CardContent>

      <CardFooter className="flex flex-col gap-2 mt-auto">
        {hasAdditionals && (
          <span className="text-sm text-green-600 font-medium">+ {formatCurrency(additionalsPrice)}/ano de adicionais</span>
        )}

        {onCustomize && (
          <Button
            className="w-full rounded-full text-[#0071e2] hover:text-[#0071e2]/90 hover:bg-[#0071e2]/10 text-base font-medium group relative overflow-hidden transition-all duration-300"
            variant="outline"
            onClick={(e) => {
              e.stopPropagation();
              onCustomize();
            }}
          >
            <span className="relative z-10 flex items-center justify-center gap-2">
              Personalizar
            </span>
          </Button>
        )}

        {/* Current plan message */}
        {isCurrentPlan && (
          <p className="text-sm text-amber-600 font-medium mt-1 text-center">
            Este é seu plano, você pode adicionar mais leads ou recursos extras.
          </p>
        )}

        {/* Immediate upgrade button - same billing cycle (monthly->monthly or yearly->yearly) */}
        {onSelect && isImmediateUpgrade && !disabled && (
          <Button
            className="w-full bg-[#0071e2] hover:bg-[#0071e2]/90 rounded-full text-base font-medium group relative overflow-hidden transition-all duration-300 text-white"
            onClick={onSelect}
          >
            {isSelected && <motion.div
              initial={{ scale: 0.5, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{
                type: 'spring',
                stiffness: 300,
                damping: 20,
              }}
              className="relative"
            >
              <Check className="w-3.5 h-3.5 " strokeWidth={3} />
            </motion.div>}
            <span className="relative z-10 flex items-center justify-center gap-2">
              {isPartialUpgrade ? t('common.partialUpgrade') : t('common.upgrade')}
            </span>
          </Button>
        )}

        {/* Scheduled upgrade button - different billing cycle (monthly->yearly) */}
        {onSelect && isScheduledUpgrade && !disabled && (!isCurrentPlan || isPartialUpgrade) && (
          <>
            <Button
              className="w-full bg-[#0071e2] hover:bg-[#0071e2]/90 rounded-full text-base font-medium group relative overflow-hidden transition-all duration-300 text-white"
              onClick={onSelect}
            >

              {isSelected && <motion.div
                initial={{ scale: 0.5, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{
                  type: 'spring',
                  stiffness: 300,
                  damping: 20,
                }}
                className="relative"
              >
                <Check className="w-3.5 h-3.5 " strokeWidth={3} />
              </motion.div>}
              <span className="relative z-10 flex items-center justify-center gap-2">
                {isPartialUpgrade ? t('common.partialUpgrade') : t('common.upgrade')}
              </span>
            </Button>
            <p className="text-xs text-amber-600 font-medium mt-1">
              * Será aplicado em {formattedNextBillingDate}
            </p>
          </>
        )}

        {/* Scheduled downgrade button */}
        {onSelect && isScheduledDowngrade && !isCurrentPlan && !disabled && (
          <>
            <Button
              className="w-full bg-[#0071e2] hover:bg-[#0071e2]/90 rounded-full text-base font-medium group relative overflow-hidden transition-all duration-300 text-white"
              onClick={onSelect}
            >

              {isSelected && <motion.div
                initial={{ scale: 0.5, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{
                  type: 'spring',
                  stiffness: 300,
                  damping: 20,
                }}
                className="relative"
              >
                <Check className="w-3.5 h-3.5 " strokeWidth={3} />
              </motion.div>}
              <span className="relative z-10 flex items-center justify-center gap-2">
                {t('common.downgrade')}
              </span>
            </Button>
            <p className="text-xs text-amber-600 font-medium mt-1">
              * Será aplicado em {formattedNextBillingDate}
            </p>
          </>
        )}

        {/* Regular select button for new users (no current plan) */}
        {onSelect && !currentPlan && !disabled && (
          <Button
            className="w-full bg-[#0071e2] hover:bg-[#0071e2]/90 rounded-full text-base font-medium group relative overflow-hidden transition-all duration-300 text-white"
            onClick={onSelect}
          >
            {isSelected && <motion.div
              initial={{ scale: 0.5, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{
                type: 'spring',
                stiffness: 300,
                damping: 20,
              }}
              className="relative"
            >
              <Check className="w-3.5 h-3.5 " strokeWidth={3} />
            </motion.div>}
            <span className="relative z-10 flex items-center justify-center gap-2">
              {isSelected ? t('common.selected') : t('common.select')}
            </span>
          </Button>
        )}

        <SecurityBadges />
      </CardFooter>
    </Card>
  );
}
