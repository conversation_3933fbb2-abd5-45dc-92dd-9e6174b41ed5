import { cn } from "@/lib/utils";
import React from "react";
interface GradientTitleProps {
  children: React.ReactNode;
  className?: string;
}

export const GradientTitle: React.FC<GradientTitleProps> = ({
  children,
  className,
}) => {
  return (
    <div
      className={cn(
        "text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-cyan-500",
        className
      )}
    >
      {children}
    </div>
  );
};
